<?php
function handle_surgeries($action, $method, $db)
{
    switch ($action) {
        case 'add':
            if ($method === 'POST') {
                $date = trim($_POST['date'] ?? '');
                $notes = trim($_POST['notes'] ?? '');
                $status = trim($_POST['status'] ?? '');
                $graft_count = $_POST['graft_count'] ?? null;
                $patient_id = $_POST['patient_id'] ?? null;
                $room_id = $_POST['room_id'] ?? null;

                if ($date && $status && $patient_id) {
                    try {
                        $db->beginTransaction();

                        // Insert surgery
                        $stmt = $db->prepare("INSERT INTO surgeries (date, notes, status, graft_count, patient_id, is_recorded, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))");
                        $stmt->execute([$date, $notes, $status, $graft_count, $patient_id, TRUE]);
                        $surgery_id = $db->lastInsertId();

                        // Create room reservation if room is selected
                        if ($room_id) {
                            // Check if room is available
                            $stmt = $db->prepare("SELECT id FROM room_reservations WHERE room_id = ? AND reserved_date = ?");
                            $stmt->execute([$room_id, $date]);
                            if ($stmt->fetch()) {
                                $db->rollBack();
                                return ['success' => false, 'error' => 'Selected room is already booked for this date.'];
                            }

                            // Create reservation
                            $stmt = $db->prepare("INSERT INTO room_reservations (room_id, surgery_id, reserved_date, created_at) VALUES (?, ?, ?, datetime('now'))");
                            $stmt->execute([$room_id, $surgery_id, $date]);
                        }

                        $db->commit();
                        return ['success' => true, 'id' => $surgery_id, 'message' => 'Surgery added successfully.'];
                    } catch (Exception $e) {
                        $db->rollBack();
                        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
                    }
                }
                return ['success' => false, 'error' => 'Date, status, and patient_id are required.'];
            }
            break;

        case 'update':
            if ($method === 'POST') {
                $id = $_POST['id'] ?? null;
                $date = trim($_POST['date'] ?? '');
                $notes = trim($_POST['notes'] ?? '');
                $status = trim($_POST['status'] ?? '');
                $graft_count = $_POST['graft_count'] ?? null;
                $patient_id = $_POST['patient_id'] ?? null;
                $room_id = $_POST['room_id'] ?? null;

                if ($id && $date && $status && $patient_id) {
                    try {
                        $db->beginTransaction();

                        // Update surgery
                        $stmt = $db->prepare("UPDATE surgeries SET date = ?, notes = ?, status = ?, graft_count = ?, patient_id = ?, updated_at = datetime('now') WHERE id = ?");
                        $stmt->execute([$date, $notes, $status, $graft_count, $patient_id, $id]);

                        // Handle room reservation
                        // First, remove existing reservation for this surgery
                        $stmt = $db->prepare("DELETE FROM room_reservations WHERE surgery_id = ?");
                        $stmt->execute([$id]);

                        // Create new reservation if room is selected
                        if ($room_id) {
                            // Check if room is available (excluding current surgery)
                            $stmt = $db->prepare("SELECT id FROM room_reservations WHERE room_id = ? AND reserved_date = ? AND surgery_id != ?");
                            $stmt->execute([$room_id, $date, $id]);
                            if ($stmt->fetch()) {
                                $db->rollBack();
                                return ['success' => false, 'error' => 'Selected room is already booked for this date.'];
                            }

                            // Create new reservation
                            $stmt = $db->prepare("INSERT INTO room_reservations (room_id, surgery_id, reserved_date, created_at) VALUES (?, ?, ?, datetime('now'))");
                            $stmt->execute([$room_id, $id, $date]);
                        }

                        $db->commit();
                        return ['success' => true, 'message' => 'Surgery updated successfully.'];
                    } catch (Exception $e) {
                        $db->rollBack();
                        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
                    }
                }
                return ['success' => false, 'error' => 'id, date, status, and patient_id are required.'];
            }
            break;

        case 'delete':
            if ($method === 'POST') {
                $id = $_POST['id'] ?? null;
                error_log("Surgeries delete handler reached. ID: " . $id); // Add logging
                if ($id) {
                    $stmt = $db->prepare("DELETE FROM surgeries WHERE id = ?");
                    $stmt->execute([$id]);
                    $response = ['success' => true];
                    error_log("Surgeries delete successful. Response: " . json_encode($response)); // Add logging
                    return $response;
                }
                $response = ['success' => false, 'error' => 'ID is required.'];
                error_log("Surgeries delete failed: ID missing. Response: " . json_encode($response)); // Add logging
                return $response;
            }
            break;

        case 'get':
            if ($method === 'GET') {
                $id = $_GET['id'] ?? null;
                if ($id) {
                    $stmt = $db->prepare("
                        SELECT s.*, p.name as patient_name, a.name as agency_name,
                               rr.room_id, r.name as room_name
                        FROM surgeries s
                        LEFT JOIN patients p ON s.patient_id = p.id
                        LEFT JOIN agencies a ON p.agency_id = a.id
                        LEFT JOIN room_reservations rr ON s.id = rr.surgery_id
                        LEFT JOIN rooms r ON rr.room_id = r.id
                        WHERE s.id = ?
                    ");
                    $stmt->execute([$id]);
                    $data = $stmt->fetch(PDO::FETCH_ASSOC);
                    return $data ? ['success' => true, 'surgery' => $data] : ['success' => false, 'error' => "Surgery not found with ID: {$id}"];
                }
                return ['success' => false, 'error' => 'ID is required.'];
            }
            break;

        case 'list':
            if ($method === 'GET') {
                $patient_id = $_GET['patient_id'] ?? null;
                if ($patient_id) {
                    $stmt = $db->prepare("SELECT s.*, p.name as patient_name, a.name as agency_name FROM surgeries s JOIN patients p ON s.patient_id = p.id LEFT JOIN agencies a ON p.agency_id = a.id WHERE s.patient_id = ? ORDER BY s.date DESC");
                    $stmt->execute([$patient_id]);
                    return ['success' => true, 'surgeries' => $stmt->fetchAll(PDO::FETCH_ASSOC)];
                } else {
                    // Apply agency filtering for non-admin users
                    $sql = "SELECT s.*, p.name as patient_name, a.name as agency_name FROM surgeries s LEFT JOIN patients p ON s.patient_id = p.id LEFT JOIN agencies a ON p.agency_id = a.id";
                    $params = [];

                    // If user is not admin, restrict to their agency
                    if ($_SESSION['role'] !== 'admin' && $_SESSION['agency_id']) {
                        $sql .= " WHERE p.agency_id = ?";
                        $params[] = $_SESSION['agency_id'];
                    }

                    $sql .= " ORDER BY s.date DESC";

                    $stmt = $db->prepare($sql);
                    $stmt->execute($params);
                    return ['success' => true, 'surgeries' => $stmt->fetchAll(PDO::FETCH_ASSOC)];
                }
            }
            break;
    }

    return ['success' => false, 'error' => "Invalid request for action '{$action}' with method '{$method}'."];
}