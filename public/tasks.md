add all new tables and related fields like rooms, technicians and etc to database.sql to use later 


if user role is agent
    at add_edit_patient.php
    - update the request and fetch the patients with current users agency only you may add another variable like agency=agency_id to api.php?entity=patients&action=list 
    - dont make request to http://localhost/api.php?entity=agencies&action=list and hide agency field and set this field value as user agency id  
   
    at patients.php 
    -search only patients with current user agency id
   - update the request and fetch the patients with current users agency only you may add another variable like agency=agency_id to api.php?entity=patients&action=list 
   
   at surgeries.php
   - search only surgeries with current user agency id
   - update the request and fetch the surgeries with current users agency only you may add another variable like agency=agency_id to api.php?entity=surgeries&action=list 
   - disable edit button if the status is completed
   
   at add_edit_surgery.php
   - while creating new patient add current user agency id to patient form 
   - update the request and fetch the patients with current users agency only you may add another variable like agency=agency_id to api.php?entity=patients&action=list 
   - update the request and fetch only avaliable rooms at selected date at this query api.php?entity=rooms&action=list you may add another variable like date= selected date
   - dont make request to http://localhost/api.php?entity=agencies&action=list and hide agency field and set this field value as user agency id  
   at room_availability.php
   - disable all fields and save button if the status is completed
   
if user role is editor
    at add_edit_patient.php
    - disable agency field while editing record append small edit icon button to make it enable again and add    <small id="info" class="form-text text-muted">Dont change agency if you are not sure !</small> next to input
   
    at surgeries.php
    - disable all fields except status fieldand disable save button if the status is completed 
 

